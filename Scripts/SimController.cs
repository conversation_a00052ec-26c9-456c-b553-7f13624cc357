using UnityEngine;

public class SimController : MonoBehaviour
{
    [Header("Scene Setup")]
    public bool createBeachScene = true;
    public bool removeExistingSphere = true;

    [Header("Beach Settings")]
    public Vector3 beachPosition = new Vector3(-50f, 0f, 0f);
    public Vector3 beachScale = Vector3.one;

    [Header("Ocean Settings")]
    public Vector3 oceanPosition = new Vector3(0f, 0f, 0f);
    public Vector3 oceanScale = Vector3.one;

    [Header("Camera Settings")]
    public Vector3 cameraPosition = new Vector3(0f, 15f, -30f);
    public Vector3 cameraRotation = new Vector3(15f, 0f, 0f);

    [Header("Lighting Settings")]
    public Vector3 sunRotation = new Vector3(45f, -30f, 0f);
    public Color sunColor = new Color(1f, 0.95f, 0.8f, 1f);
    public float sunIntensity = 2f;

    private GameObject beachTerrain;
    private GameObject oceanWater;
    private Camera mainCamera;
    private Light directionalLight;

    void Start()
    {
        if (createBeachScene)
        {
            SetupBeachScene();
        }
    }

    public void SetupBeachScene()
    {
        // Remove existing blue sphere if requested
        if (removeExistingSphere)
        {
            RemoveExistingSphere();
        }

        // Create beach terrain
        CreateBeachTerrain();

        // Create ocean water
        CreateOceanWater();

        // Setup camera
        SetupCamera();

        // Setup lighting
        SetupLighting();

        Debug.Log("Beach scene setup complete!");
    }

    void RemoveExistingSphere()
    {
        GameObject blueSphere = GameObject.Find("Blue Sphere");
        if (blueSphere != null)
        {
            DestroyImmediate(blueSphere);
            Debug.Log("Removed existing blue sphere");
        }
    }

    void CreateBeachTerrain()
    {
        beachTerrain = new GameObject("Beach Terrain");
        beachTerrain.transform.position = beachPosition;
        beachTerrain.transform.localScale = beachScale;

        BeachTerrain terrainScript = beachTerrain.AddComponent<BeachTerrain>();

        // Configure terrain settings
        terrainScript.width = 100;
        terrainScript.height = 50;
        terrainScript.scale = 1f;
        terrainScript.maxHeight = 8f;
        terrainScript.noiseScale = 0.05f;
        terrainScript.noiseStrength = 1.5f;

        Debug.Log("Beach terrain created");
    }

    void CreateOceanWater()
    {
        oceanWater = new GameObject("Ocean Water");
        oceanWater.transform.position = oceanPosition;
        oceanWater.transform.localScale = oceanScale;

        OceanWaves oceanScript = oceanWater.AddComponent<OceanWaves>();

        // Configure ocean settings
        oceanScript.oceanWidth = 200;
        oceanScript.oceanHeight = 200;
        oceanScript.scale = 1f;
        oceanScript.waveHeight = 1.5f;
        oceanScript.waveSpeed = 0.8f;
        oceanScript.waveFrequency = 0.08f;
        oceanScript.secondaryWaveHeight = 0.8f;
        oceanScript.secondaryWaveSpeed = 1.2f;
        oceanScript.secondaryWaveFrequency = 0.12f;

        Debug.Log("Ocean water created");
    }

    void SetupCamera()
    {
        mainCamera = Camera.main;
        if (mainCamera == null)
        {
            mainCamera = FindFirstObjectByType<Camera>();
        }

        if (mainCamera != null)
        {
            mainCamera.transform.position = cameraPosition;
            mainCamera.transform.rotation = Quaternion.Euler(cameraRotation);

            // Adjust camera settings for better beach view
            mainCamera.farClipPlane = 1000f;
            mainCamera.fieldOfView = 60f;

            Debug.Log("Camera positioned for beach view");
        }
    }

    void SetupLighting()
    {
        // Find the directional light
        directionalLight = FindFirstObjectByType<Light>();

        if (directionalLight != null && directionalLight.type == LightType.Directional)
        {
            directionalLight.transform.rotation = Quaternion.Euler(sunRotation);
            directionalLight.color = sunColor;
            directionalLight.intensity = sunIntensity;

            // Enable shadows for better depth
            directionalLight.shadows = LightShadows.Soft;

            Debug.Log("Sun lighting configured");
        }

        // Set ambient lighting for beach atmosphere
        RenderSettings.ambientMode = UnityEngine.Rendering.AmbientMode.Trilight;
        RenderSettings.ambientSkyColor = new Color(0.5f, 0.7f, 1f, 1f); // Light blue sky
        RenderSettings.ambientEquatorColor = new Color(0.4f, 0.4f, 0.4f, 1f); // Neutral horizon
        RenderSettings.ambientGroundColor = new Color(0.3f, 0.25f, 0.2f, 1f); // Warm ground
    }

    // Method to regenerate the entire scene
    public void RegenerateScene()
    {
        // Clean up existing objects
        if (beachTerrain != null) DestroyImmediate(beachTerrain);
        if (oceanWater != null) DestroyImmediate(oceanWater);

        // Recreate scene
        SetupBeachScene();
    }
}
