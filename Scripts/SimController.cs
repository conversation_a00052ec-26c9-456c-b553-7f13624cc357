using UnityEngine;

public class SimController : MonoBehaviour
{
    [Header("Blue Sphere Settings")]
    public float sphereDistance = 5f; // Distance from camera
    public float sphereSize = 1f; // Diameter of the sphere

    private GameObject blueSphere;
    private Camera mainCamera;

    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        // Find the main camera
        mainCamera = Camera.main;
        if (mainCamera == null)
        {
            mainCamera = FindFirstObjectByType<Camera>();
        }

        if (mainCamera != null)
        {
            CreateBlueSphere();
        }
        else
        {
            Debug.LogError("No camera found in the scene!");
        }
    }

    void CreateBlueSphere()
    {
        // Create a sphere GameObject
        blueSphere = GameObject.CreatePrimitive(PrimitiveType.Sphere);
        blueSphere.name = "Blue Sphere";

        // Set the sphere size
        blueSphere.transform.localScale = Vector3.one * sphereSize;

        // Position the sphere in front of the camera
        Vector3 spherePosition = mainCamera.transform.position + mainCamera.transform.forward * sphereDistance;
        blueSphere.transform.position = spherePosition;

        // Create a blue material
        Material blueMaterial = new Material(Shader.Find("Universal Render Pipeline/Lit"));
        blueMaterial.color = Color.blue;

        // Apply the material to the sphere
        Renderer sphereRenderer = blueSphere.GetComponent<Renderer>();
        sphereRenderer.material = blueMaterial;

        Debug.Log($"Blue sphere created at position: {spherePosition}");
    }

    // Update is called once per frame
    void Update()
    {
        // Keep the sphere centered in front of the camera
        if (blueSphere != null && mainCamera != null)
        {
            Vector3 targetPosition = mainCamera.transform.position + mainCamera.transform.forward * sphereDistance;
            blueSphere.transform.position = targetPosition;
        }
    }
}
