using UnityEngine;

[RequireComponent(typeof(MeshFilter), typeof(MeshRenderer))]
public class BeachTerrain : MonoBehaviour
{
    [Header("Terrain Settings")]
    public int width = 100;
    public int height = 100;
    public float scale = 1f;
    public float beachSlope = 0.1f;
    public float maxHeight = 5f;
    
    [Header("Noise Settings")]
    public float noiseScale = 0.1f;
    public float noiseStrength = 2f;
    public Vector2 noiseOffset = Vector2.zero;
    
    private MeshFilter meshFilter;
    private MeshRenderer meshRenderer;
    private Mesh terrainMesh;

    void Start()
    {
        meshFilter = GetComponent<MeshFilter>();
        meshRenderer = GetComponent<MeshRenderer>();
        GenerateBeachTerrain();
        CreateSandMaterial();
    }

    void GenerateBeachTerrain()
    {
        terrainMesh = new Mesh();
        terrainMesh.name = "Beach Terrain";

        // Create vertices
        Vector3[] vertices = new Vector3[(width + 1) * (height + 1)];
        Vector2[] uvs = new Vector2[vertices.Length];
        
        for (int i = 0, z = 0; z <= height; z++)
        {
            for (int x = 0; x <= width; x++)
            {
                float xPos = x * scale;
                float zPos = z * scale;
                
                // Create beach slope (higher towards back, lower towards water)
                float baseHeight = (float)z / height * maxHeight;
                
                // Add Perlin noise for natural variation
                float noiseValue = Mathf.PerlinNoise(
                    (x + noiseOffset.x) * noiseScale, 
                    (z + noiseOffset.y) * noiseScale
                ) * noiseStrength;
                
                float finalHeight = baseHeight + noiseValue;
                
                // Ensure water edge is at y=0
                if (z == 0) finalHeight = 0f;
                
                vertices[i] = new Vector3(xPos, finalHeight, zPos);
                uvs[i] = new Vector2((float)x / width, (float)z / height);
                i++;
            }
        }

        // Create triangles
        int[] triangles = new int[width * height * 6];
        int vert = 0;
        int tris = 0;

        for (int z = 0; z < height; z++)
        {
            for (int x = 0; x < width; x++)
            {
                triangles[tris + 0] = vert + 0;
                triangles[tris + 1] = vert + width + 1;
                triangles[tris + 2] = vert + 1;
                triangles[tris + 3] = vert + 1;
                triangles[tris + 4] = vert + width + 1;
                triangles[tris + 5] = vert + width + 2;

                vert++;
                tris += 6;
            }
            vert++;
        }

        terrainMesh.vertices = vertices;
        terrainMesh.triangles = triangles;
        terrainMesh.uv = uvs;
        terrainMesh.RecalculateNormals();

        meshFilter.mesh = terrainMesh;
    }

    void CreateSandMaterial()
    {
        Material sandMaterial = new Material(Shader.Find("Universal Render Pipeline/Lit"));
        sandMaterial.name = "Sand Material";
        
        // Set sand color (warm beige)
        sandMaterial.color = new Color(0.96f, 0.87f, 0.70f, 1f);
        
        // Adjust material properties for sand
        sandMaterial.SetFloat("_Smoothness", 0.1f); // Rough surface
        sandMaterial.SetFloat("_Metallic", 0f); // Non-metallic
        
        meshRenderer.material = sandMaterial;
    }

    // Method to regenerate terrain (useful for runtime changes)
    public void RegenerateTerrain()
    {
        GenerateBeachTerrain();
    }

    void OnValidate()
    {
        // Regenerate terrain when values change in editor
        if (Application.isPlaying && meshFilter != null)
        {
            RegenerateTerrain();
        }
    }
}
