using UnityEngine;

[RequireComponent(typeof(MeshFilter), typeof(MeshRenderer))]
public class OceanWaves : MonoBehaviour
{
    [Header("Ocean Settings")]
    public int oceanWidth = 200;
    public int oceanHeight = 200;
    public float scale = 1f;
    
    [Header("Wave Settings")]
    public float waveHeight = 2f;
    public float waveSpeed = 1f;
    public float waveFrequency = 0.1f;
    public Vector2 waveDirection = new Vector2(1f, 0.5f);
    
    [Header("Secondary Waves")]
    public float secondaryWaveHeight = 1f;
    public float secondaryWaveSpeed = 1.5f;
    public float secondaryWaveFrequency = 0.15f;
    public Vector2 secondaryWaveDirection = new Vector2(-0.5f, 1f);

    [Header("Water Level")]
    public float baseWaterLevel = 0f;
    
    private MeshFilter meshFilter;
    private MeshRenderer meshRenderer;
    private Mesh oceanMesh;
    private Vector3[] baseVertices;
    private Vector3[] vertices;

    void Start()
    {
        meshFilter = GetComponent<MeshFilter>();
        meshRenderer = GetComponent<MeshRenderer>();
        GenerateOceanMesh();
        CreateWaterMaterial();
    }

    void GenerateOceanMesh()
    {
        oceanMesh = new Mesh();
        oceanMesh.name = "Ocean Mesh";

        // Create vertices
        baseVertices = new Vector3[(oceanWidth + 1) * (oceanHeight + 1)];
        vertices = new Vector3[baseVertices.Length];
        Vector2[] uvs = new Vector2[baseVertices.Length];
        
        for (int i = 0, z = 0; z <= oceanHeight; z++)
        {
            for (int x = 0; x <= oceanWidth; x++)
            {
                float xPos = x * scale;
                float zPos = z * scale;
                
                baseVertices[i] = new Vector3(xPos, baseWaterLevel, zPos);
                uvs[i] = new Vector2((float)x / oceanWidth, (float)z / oceanHeight);
                i++;
            }
        }

        // Create triangles
        int[] triangles = new int[oceanWidth * oceanHeight * 6];
        int vert = 0;
        int tris = 0;

        for (int z = 0; z < oceanHeight; z++)
        {
            for (int x = 0; x < oceanWidth; x++)
            {
                triangles[tris + 0] = vert + 0;
                triangles[tris + 1] = vert + oceanWidth + 1;
                triangles[tris + 2] = vert + 1;
                triangles[tris + 3] = vert + 1;
                triangles[tris + 4] = vert + oceanWidth + 1;
                triangles[tris + 5] = vert + oceanWidth + 2;

                vert++;
                tris += 6;
            }
            vert++;
        }

        oceanMesh.vertices = baseVertices;
        oceanMesh.triangles = triangles;
        oceanMesh.uv = uvs;
        oceanMesh.RecalculateNormals();

        meshFilter.mesh = oceanMesh;
    }

    void Update()
    {
        AnimateWaves();
    }

    void AnimateWaves()
    {
        float time = Time.time;
        
        for (int i = 0; i < baseVertices.Length; i++)
        {
            Vector3 baseVertex = baseVertices[i];
            
            // Primary wave
            float wave1 = Mathf.Sin(
                (baseVertex.x * waveDirection.x + baseVertex.z * waveDirection.y) * waveFrequency + 
                time * waveSpeed
            ) * waveHeight;
            
            // Secondary wave for more complex motion
            float wave2 = Mathf.Sin(
                (baseVertex.x * secondaryWaveDirection.x + baseVertex.z * secondaryWaveDirection.y) * secondaryWaveFrequency + 
                time * secondaryWaveSpeed
            ) * secondaryWaveHeight;
            
            vertices[i] = new Vector3(
                baseVertex.x,
                baseWaterLevel + wave1 + wave2,
                baseVertex.z
            );
        }
        
        oceanMesh.vertices = vertices;
        oceanMesh.RecalculateNormals();
    }

    void CreateWaterMaterial()
    {
        Material waterMaterial = new Material(Shader.Find("Universal Render Pipeline/Lit"));
        waterMaterial.name = "Ocean Water Material";
        
        // Set water color (deep blue-green)
        waterMaterial.color = new Color(0.1f, 0.3f, 0.6f, 0.8f);
        
        // Enable transparency
        waterMaterial.SetFloat("_Surface", 1); // Transparent
        waterMaterial.SetFloat("_Blend", 0); // Alpha blend
        
        // Water properties
        waterMaterial.SetFloat("_Smoothness", 0.9f); // Very smooth
        waterMaterial.SetFloat("_Metallic", 0.1f); // Slightly metallic for reflection
        
        // Enable transparency in render queue
        waterMaterial.renderQueue = 3000;
        
        meshRenderer.material = waterMaterial;
    }
}
